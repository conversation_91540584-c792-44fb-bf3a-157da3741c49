package com.pass.hbl.manager.backend.persistence.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Utility component for batch deletion operations with season-specific filtering
 */
@Component
@Slf4j
public class BatchDeletionUtil {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * Delete records by previous season league IDs with configurable batch size and wait time
     */
    @Transactional
    public long deleteBySeasonIds(String tableName, List<String> previousSeasonLeagueIds, int batchSize, long waitTimeMs) {
        if (CollectionUtils.isEmpty(previousSeasonLeagueIds)) {
            log.info("No season league IDs provided for table {}, skipping deletion", tableName);
            return 0L;
        }

        if (previousSeasonLeagueIds.size() == 1) {
            log.info("Deleting from {} for the last previous season in batches of {}", tableName, batchSize);
        } else {
            log.info("Deleting from {} for {} previous seasons in batches of {}", tableName, previousSeasonLeagueIds.size(), batchSize);
        }

        String leagueIdsStr = previousSeasonLeagueIds.stream()
            .map(id -> "'" + id + "'")
            .collect(Collectors.joining(","));

        String deleteQuery = buildDeleteQuery(tableName, leagueIdsStr, batchSize);

        int rowsAffected;
        long totalDeleted = 0;

        do {
            rowsAffected = jdbcTemplate.update(deleteQuery);
            totalDeleted += rowsAffected;
            log.info("Deleted {} rows from {}, total: {}", rowsAffected, tableName, totalDeleted);

            // Configurable pause to reduce DB load
            if (rowsAffected > 0 && waitTimeMs > 0) {
                try {
                    Thread.sleep(waitTimeMs);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("Deletion interrupted for table {}", tableName);
                    break;
                }
            }
        } while (rowsAffected > 0);

        if (previousSeasonLeagueIds.size() == 1) {
            log.info("Completed deletion from {} for the last previous season. Total rows deleted: {}", tableName, totalDeleted);
        } else {
            log.info("Completed deletion from {} for previous seasons. Total rows deleted: {}", tableName, totalDeleted);
        }
        return totalDeleted;
    }

    /**
     * Count records that would be deleted by previous season league IDs
     */
    public long countBySeasonIds(String tableName, List<String> previousSeasonLeagueIds) {
        if (CollectionUtils.isEmpty(previousSeasonLeagueIds)) {
            return 0L;
        }

        String leagueIdsStr = previousSeasonLeagueIds.stream()
            .map(id -> "'" + id + "'")
            .collect(Collectors.joining(","));

        String countQuery = buildCountQuery(tableName, leagueIdsStr);

        try {
            Long count = jdbcTemplate.queryForObject(countQuery, Long.class);
            return count != null ? count : 0L;
        } catch (Exception e) {
            log.error("Error counting records for table {} with season leagues {}: {}", tableName, previousSeasonLeagueIds, e.getMessage());
            return 0L;
        }
    }

    /**
     * Build appropriate DELETE query based on table structure
     */
    private String buildDeleteQuery(String tableName, String leagueIdsStr, int batchSize) {
        switch (tableName) {
            case "transfer_market_bid":
                return String.format(
                    "DELETE FROM hm.transfer_market_bid WHERE id IN (" +
                    "SELECT tmb.id FROM hm.transfer_market_bid tmb " +
                    "JOIN hm.transfer_market tm ON tmb.transfer_market_id = tm.id " +
                    "WHERE tm.league_id IN (%s) AND tmb.deleted = false LIMIT %d)",
                    leagueIdsStr, batchSize);

            case "transfer_market":
                return String.format(
                    "DELETE FROM hm.transfer_market WHERE league_id IN (%s) AND deleted = false LIMIT %d",
                    leagueIdsStr, batchSize);

            case "lineup":
                return String.format(
                    "DELETE FROM hm.lineup WHERE id IN (" +
                    "SELECT l.id FROM hm.lineup l " +
                    "JOIN hm.round r ON l.round_id = r.id " +
                    "JOIN hm.league lg ON r.season_id = lg.season_id " +
                    "WHERE lg.id IN (%s) AND l.deleted = false LIMIT %d)",
                    leagueIdsStr, batchSize);

            case "user_round_score":
                return String.format(
                    "DELETE FROM hm.user_round_score WHERE league_id IN (%s) AND deleted = false LIMIT %d",
                    leagueIdsStr, batchSize);

            case "team":
                return String.format(
                    "DELETE FROM hm.team WHERE league_id IN (%s) AND deleted = false LIMIT %d",
                    leagueIdsStr, batchSize);

            case "league_invitation":
                return String.format(
                    "DELETE FROM hm.league_invitation WHERE league_id IN (%s) AND deleted = false LIMIT %d",
                    leagueIdsStr, batchSize);

            case "user_notification":
                return String.format(
                    "DELETE FROM hm.user_notification WHERE id IN (" +
                    "SELECT un.id FROM hm.user_notification un " +
                    "WHERE un.created_at < (SELECT MIN(s.end_date) FROM hm.season s " +
                    "JOIN hm.league l ON s.id = l.season_id WHERE l.id IN (%s)) " +
                    "AND un.deleted = false LIMIT %d)",
                    leagueIdsStr, batchSize);

            case "scheduler_job":
                return String.format(
                    "DELETE FROM hm.scheduler_job WHERE id IN (" +
                    "SELECT sj.id FROM hm.scheduler_job sj " +
                    "WHERE sj.created_at < (SELECT MIN(s.end_date) FROM hm.season s " +
                    "JOIN hm.league l ON s.id = l.season_id WHERE l.id IN (%s)) " +
                    "AND sj.deleted = false LIMIT %d)",
                    leagueIdsStr, batchSize);

            default:
                throw new IllegalArgumentException("Unsupported table for deletion: " + tableName);
        }
    }

    /**
     * Build appropriate COUNT query based on table structure
     */
    private String buildCountQuery(String tableName, String leagueIdsStr) {
        switch (tableName) {
            case "transfer_market_bid":
                return String.format(
                    "SELECT COUNT(*) FROM hm.transfer_market_bid tmb " +
                    "JOIN hm.transfer_market tm ON tmb.transfer_market_id = tm.id " +
                    "WHERE tm.league_id IN (%s) AND tmb.deleted = false",
                    leagueIdsStr);

            case "transfer_market":
                return String.format(
                    "SELECT COUNT(*) FROM hm.transfer_market WHERE league_id IN (%s) AND deleted = false",
                    leagueIdsStr);

            case "lineup":
                return String.format(
                    "SELECT COUNT(*) FROM hm.lineup l " +
                    "JOIN hm.round r ON l.round_id = r.id " +
                    "JOIN hm.league lg ON r.season_id = lg.season_id " +
                    "WHERE lg.id IN (%s) AND l.deleted = false",
                    leagueIdsStr);

            case "user_round_score":
                return String.format(
                    "SELECT COUNT(*) FROM hm.user_round_score WHERE league_id IN (%s) AND deleted = false",
                    leagueIdsStr);

            case "team":
                return String.format(
                    "SELECT COUNT(*) FROM hm.team WHERE league_id IN (%s) AND deleted = false",
                    leagueIdsStr);

            case "league_invitation":
                return String.format(
                    "SELECT COUNT(*) FROM hm.league_invitation WHERE league_id IN (%s) AND deleted = false",
                    leagueIdsStr);

            case "user_notification":
                return String.format(
                    "SELECT COUNT(*) FROM hm.user_notification un " +
                    "WHERE un.created_at < (SELECT MIN(s.end_date) FROM hm.season s " +
                    "JOIN hm.league l ON s.id = l.season_id WHERE l.id IN (%s)) " +
                    "AND un.deleted = false",
                    leagueIdsStr);

            case "scheduler_job":
                return String.format(
                    "SELECT COUNT(*) FROM hm.scheduler_job sj " +
                    "WHERE sj.created_at < (SELECT MIN(s.end_date) FROM hm.season s " +
                    "JOIN hm.league l ON s.id = l.season_id WHERE l.id IN (%s)) " +
                    "AND sj.deleted = false",
                    leagueIdsStr);

            default:
                throw new IllegalArgumentException("Unsupported table for counting: " + tableName);
        }
    }
}
